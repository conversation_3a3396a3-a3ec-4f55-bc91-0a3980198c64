"""
钱包管理器。

提供钱包的创建、存储、检索和管理功能。
"""

import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone
from eth_account import Account
from eth_utils import is_hex_address, to_checksum_address

from .exceptions import (
    WalletError, WalletNotFoundError, PrivateKeyError
)


logger = logging.getLogger(__name__)


class WalletRecord:
    """钱包记录数据模型。"""
    
    def __init__(
        self,
        id: Optional[int] = None,
        name: str = "",
        address: str = "",
        encrypted_private_key: str = "",
        created_at: Optional[datetime] = None,
        updated_at: Optional[datetime] = None
    ):
        self.id = id
        self.name = name
        self.address = address
        self.encrypted_private_key = encrypted_private_key
        self.created_at = created_at or datetime.now(timezone.utc)
        self.updated_at = updated_at or datetime.now(timezone.utc)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式。"""
        return {
            'id': self.id,
            'name': self.name,
            'address': self.address,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class WalletManager:
    """
    钱包管理器。
    
    提供钱包的安全存储、检索和管理功能。
    """
    
    def __init__(self, storage):
        """
        初始化钱包管理器。
        
        Args:
            storage: 数据存储实例
        """
        self.storage = storage
        self.logger = logger
        
        # 确保钱包表存在
        self._ensure_wallet_table()
    
    def _ensure_wallet_table(self):
        """确保钱包表存在。"""
        try:
            with self.storage.get_connection() as conn:
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS wallets (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT UNIQUE NOT NULL,
                        address TEXT NOT NULL,
                        encrypted_private_key TEXT NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                conn.commit()
                self.logger.debug("钱包表已确保存在")
        except Exception as e:
            self.logger.error(f"创建钱包表失败: {e}")
            raise WalletError(f"创建钱包表失败: {str(e)}")
    
    def add_wallet(
        self,
        name: str,
        private_key: str
    ) -> bool:
        """
        添加新钱包。

        Args:
            name: 钱包名称
            private_key: 私钥（十六进制字符串，可带或不带 0x 前缀）

        Returns:
            是否添加成功

        Raises:
            WalletError: 钱包已存在或其他错误
            PrivateKeyError: 私钥格式错误
        """
        try:
            # 验证私钥格式
            private_key = self._normalize_private_key(private_key)
            self._validate_private_key(private_key)

            # 从私钥生成地址
            account = Account.from_key(private_key)
            address = to_checksum_address(account.address)

            # 检查钱包名称是否已存在
            if self._wallet_exists(name):
                raise WalletError(f"钱包名称 '{name}' 已存在")

            # 检查地址是否已存在
            if self._address_exists(address):
                raise WalletError(f"地址 '{address}' 已存在")

            # 直接存储私钥（简化版本，不使用主密码）
            # 注意：在生产环境中应该使用更安全的存储方式

            # 存储到数据库
            with self.storage.get_connection() as conn:
                conn.execute("""
                    INSERT INTO wallets (name, address, encrypted_private_key)
                    VALUES (?, ?, ?)
                """, (name, address, private_key))  # 直接存储私钥
                conn.commit()

            self.logger.info(f"钱包 '{name}' 添加成功，地址: {address}")
            return True

        except (WalletError, PrivateKeyError):
            raise
        except Exception as e:
            self.logger.error(f"添加钱包失败: {e}")
            raise WalletError(f"添加钱包失败: {str(e)}")
    
    def get_wallet_address(self, wallet_name: str) -> str:
        """
        获取钱包地址。
        
        Args:
            wallet_name: 钱包名称
            
        Returns:
            钱包地址
            
        Raises:
            WalletNotFoundError: 钱包不存在
        """
        try:
            with self.storage.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT address FROM wallets WHERE name = ?",
                    (wallet_name,)
                )
                row = cursor.fetchone()
                
                if not row:
                    raise WalletNotFoundError(wallet_name)
                
                return row['address']
                
        except WalletNotFoundError:
            raise
        except Exception as e:
            self.logger.error(f"获取钱包地址失败: {e}")
            raise WalletError(f"获取钱包地址失败: {str(e)}")
    
    def get_private_key(self, wallet_name: str) -> str:
        """
        获取私钥。

        Args:
            wallet_name: 钱包名称

        Returns:
            私钥

        Raises:
            WalletNotFoundError: 钱包不存在
        """
        try:
            with self.storage.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT encrypted_private_key FROM wallets WHERE name = ?",
                    (wallet_name,)
                )
                row = cursor.fetchone()

                if not row:
                    raise WalletNotFoundError(wallet_name)

                stored_data = row['encrypted_private_key']

                # 检查是否是旧的加密格式
                if stored_data.startswith('{') and 'encrypted_data' in stored_data:
                    # 旧的加密格式，需要解密
                    # 但我们没有主密码，所以抛出错误
                    raise WalletError(f"钱包 '{wallet_name}' 使用旧的加密格式，请重新添加钱包")
                else:
                    # 新格式，直接返回私钥
                    return stored_data
                
        except WalletNotFoundError:
            raise
        except Exception as e:
            self.logger.error(f"获取私钥失败: {e}")
            raise WalletError(f"获取私钥失败: {str(e)}")
    
    def list_wallets(self) -> List[Dict[str, Any]]:
        """
        列出所有钱包（不包含私钥）。
        
        Returns:
            钱包列表
        """
        try:
            with self.storage.get_connection() as conn:
                cursor = conn.execute("""
                    SELECT id, name, address, created_at, updated_at 
                    FROM wallets 
                    ORDER BY created_at DESC
                """)
                
                wallets = []
                for row in cursor.fetchall():
                    wallet = {
                        'id': row['id'],
                        'name': row['name'],
                        'address': row['address'],
                        'created_at': row['created_at'],
                        'updated_at': row['updated_at']
                    }
                    wallets.append(wallet)
                
                return wallets
                
        except Exception as e:
            self.logger.error(f"列出钱包失败: {e}")
            raise WalletError(f"列出钱包失败: {str(e)}")
    
    def delete_wallet(self, wallet_name: str) -> bool:
        """
        删除钱包。
        
        Args:
            wallet_name: 钱包名称
            
        Returns:
            是否删除成功
            
        Raises:
            WalletNotFoundError: 钱包不存在
        """
        try:
            with self.storage.get_connection() as conn:
                cursor = conn.execute(
                    "DELETE FROM wallets WHERE name = ?",
                    (wallet_name,)
                )
                
                if cursor.rowcount == 0:
                    raise WalletNotFoundError(wallet_name)
                
                conn.commit()
                
            self.logger.info(f"钱包 '{wallet_name}' 删除成功")
            return True
            
        except WalletNotFoundError:
            raise
        except Exception as e:
            self.logger.error(f"删除钱包失败: {e}")
            raise WalletError(f"删除钱包失败: {str(e)}")
    
    def wallet_exists(self, wallet_name: str) -> bool:
        """检查钱包是否存在。"""
        return self._wallet_exists(wallet_name)
    
    def _wallet_exists(self, wallet_name: str) -> bool:
        """检查钱包名称是否已存在。"""
        try:
            with self.storage.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT 1 FROM wallets WHERE name = ?",
                    (wallet_name,)
                )
                return cursor.fetchone() is not None
        except Exception:
            return False
    
    def _address_exists(self, address: str) -> bool:
        """检查地址是否已存在。"""
        try:
            with self.storage.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT 1 FROM wallets WHERE address = ?",
                    (address,)
                )
                return cursor.fetchone() is not None
        except Exception:
            return False
    
    def _normalize_private_key(self, private_key: str) -> str:
        """标准化私钥格式。"""
        private_key = private_key.strip()
        
        # 移除 0x 前缀
        if private_key.startswith('0x'):
            private_key = private_key[2:]
        
        # 确保是64个字符的十六进制字符串
        if len(private_key) != 64:
            raise PrivateKeyError("私钥必须是64个字符的十六进制字符串")
        
        # 验证是否为有效的十六进制
        try:
            int(private_key, 16)
        except ValueError:
            raise PrivateKeyError("私钥包含无效的十六进制字符")
        
        return private_key
    
    def _validate_private_key(self, private_key: str) -> None:
        """验证私钥是否有效。"""
        try:
            # 尝试从私钥创建账户
            Account.from_key(private_key)
        except Exception as e:
            raise PrivateKeyError(f"无效的私钥: {str(e)}")

